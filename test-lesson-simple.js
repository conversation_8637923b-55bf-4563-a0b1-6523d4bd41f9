// Simple test to check validation
async function testLessonValidation() {
  try {
    console.log('📚 Testing lesson validation...');
    
    const lessonPayload = {
      title: 'Test Lesson Validation',
      lesson_type: 'single-lesson',
      date: new Date().toISOString(),
      start_time: new Date().toISOString(),
      end_time: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour later
      duration_minutes: 60,
      max_students: 1,
      instructor_ids: ['550e8400-e29b-41d4-a716-446655440000']
    };

    console.log('📚 Lesson payload:', JSON.stringify(lessonPayload, null, 2));

    const response = await fetch('http://localhost:3000/api/v1/lessons', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer dummy-token',
      },
      body: JSON.stringify(lessonPayload),
    });

    const data = await response.json();
    console.log('📚 Response status:', response.status);
    console.log('📚 Response data:', JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testLessonValidation();

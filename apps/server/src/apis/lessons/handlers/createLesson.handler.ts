import type { CreateLessonInput } from '../validators/createLesson.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function createLessonHandler(
  payload: CreateLessonInput,
  userId: string,
  requestId: string
): Promise<Lessons.CreateLessonResult> {
  try {
    console.log(`${requestId} [LESSONS] - CREATE - <PERSON><PERSON> started`);

    // Validate arena if provided
    if (payload.arena_id) {
      const arena = await LessonRepository.validateArena(payload.arena_id);
      if (!arena) {
        return {
          data: null,
          error: {
            code: 'ARENA_NOT_FOUND',
            message: 'Arena not found or inactive',
            statusCode: 404,
          },
        };
      }
    }

    // Validate instructors
    const instructors = await LessonRepository.validateInstructors(
      payload.instructor_ids
    );
    if (instructors.length !== payload.instructor_ids.length) {
      const foundIds = instructors.map((i: any) => i.id);
      const missingIds = payload.instructor_ids.filter(
        (id) => !foundIds.includes(id)
      );
      return {
        data: null,
        error: {
          code: 'INSTRUCTOR_NOT_FOUND',
          message: `Instructor not found: ${missingIds.join(', ')}`,
          statusCode: 404,
        },
      };
    }

    // Validate curriculum items if provided
    if (payload.curriculum_items && payload.curriculum_items.length > 0) {
      const curriculumIds = payload.curriculum_items.map(id => parseInt(id));
      const curriculumItems = await LessonRepository.validateCurriculumItems(
        curriculumIds
      );
      if (curriculumItems.length !== curriculumIds.length) {
        const foundIds = curriculumItems.map((c: any) => c.id);
        const missingIds = curriculumIds.filter(
          (id) => !foundIds.includes(id)
        );
        return {
          data: null,
          error: {
            code: 'CURRICULUM_NOT_FOUND',
            message: `Curriculum items not found: ${missingIds.join(', ')}`,
            statusCode: 404,
          },
        };
      }
    }

    // Check for scheduling conflicts if arena and instructors are provided
    if (
      payload.arena_id &&
      payload.instructor_ids &&
      payload.instructor_ids.length > 0
    ) {
      const conflictCheck = await LessonRepository.checkSchedulingConflicts(
        payload.arena_id,
        payload.instructor_ids,
        new Date(payload.start_time),
        new Date(payload.end_time)
      );

      if (conflictCheck.hasConflicts) {
        return {
          data: null,
          error: {
            code: 'SCHEDULING_CONFLICT',
            message: 'Scheduling conflict detected',
            statusCode: 409,
            details: {
              arenaConflicts: conflictCheck.arenaConflicts,
              instructorConflicts: conflictCheck.instructorConflicts,
            },
          },
        };
      }
    }

    // Prepare lesson data
    const lessonData = {
      title: payload.title,
      lesson_type: payload.lesson_type,
      arena_id: payload.arena_id || null,
      date: new Date(payload.date),
      start_time: new Date(payload.start_time),
      end_time: new Date(payload.end_time),
      duration_minutes: payload.duration_minutes,
      max_students: payload.max_students,
      current_students: 0,
      status: 'scheduled' as const,
      notes: payload.notes || null,
      require_form: payload.require_form ? 1 : 0,
      require_payment: payload.require_payment ? 1 : 0,
      price: payload.price ? payload.price.toString() : null,
      created_by: userId,
      parent_lesson_id: null,
      recurrence_pattern: payload.recurrence_pattern
        ? JSON.stringify(payload.recurrence_pattern)
        : null,
      curriculum_items: JSON.stringify(payload.curriculum_items || []),
      attachments: JSON.stringify([]),
    };

    // Handle single lesson
    if (
      payload.lesson_type === 'single-lesson' ||
      payload.lesson_type === 'camp'
    ) {
      const lesson = await LessonRepository.createLesson(
        lessonData,
        payload.instructor_ids
      );

      console.log(
        `${requestId} [LESSONS] - CREATE - Single lesson created: ${lesson?.id}`
      );

      return {
        data: { lesson: lesson as any },
        error: null,
      };
    }

    // Handle recurring lesson
    if (
      payload.lesson_type === 'recurring-lesson' &&
      payload.recurrence_pattern
    ) {
      const startDate = new Date(payload.start_time);
      const recurringDates = LessonRepository.generateRecurringDates(
        startDate,
        payload.recurrence_pattern
      );

      if (recurringDates.length === 0) {
        return {
          data: null,
          error: {
            code: 'INVALID_RECURRENCE',
            message: 'No valid dates generated for recurrence pattern',
            statusCode: 400,
          },
        };
      }

      const lessons = await LessonRepository.createRecurringLessons(
        lessonData,
        payload.instructor_ids,
        recurringDates
      );

      console.log(
        `${requestId} [LESSONS] - CREATE - Recurring lessons created: ${lessons.length} lessons`
      );

      return {
        data: { lesson: lessons[0] } as any, // Return first lesson for now
        error: null,
      };
    }

    // Should not reach here due to validation
    return {
      data: null,
      error: {
        code: 'INVALID_LESSON_TYPE',
        message: 'Invalid lesson type',
        statusCode: 400,
      },
    };
  } catch (error) {
    console.error(`${requestId} [LESSONS] - CREATE - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create lesson',
        statusCode: 500,
      },
    };
  }
}

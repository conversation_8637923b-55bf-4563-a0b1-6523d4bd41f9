import { useMutation } from '@tanstack/react-query';
import { type CreateLessonPayload } from 'shared/src/types/lessons';

import { useAuth } from './useAuthStore';

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';

export const useCreateLesson = () => {
  const { accessToken } = useAuth();

  return useMutation({
    mutationFn: async (lesson: CreateLessonPayload) => {
      console.log('🚀 ~ useCreateLesson ~ lesson payload:', JSON.stringify(lesson, null, 2));
      console.log('🚀 ~ useCreateLesson ~ accessToken:', accessToken ? 'Present' : 'Missing');

      // Use regular fetch instead of Hono client for debugging
      const res = await fetch(`${SERVER_URL}/api/v1/lessons`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(lesson),
      });

      console.log('🚀 ~ useCreateLesson ~ response status:', res.status);

      const rawData = await res.json();

      if (!res.ok) {
        throw new Error(rawData.error?.message);
      }

      return rawData;
    },
    onSuccess: (data) => {
      console.log('🚀 ~ useCreateLesson ~ data:', data);
    },
    onError: (error) => {
      console.log('🚀 ~ useCreateLesson ~ error:', error);
    },
  });
};

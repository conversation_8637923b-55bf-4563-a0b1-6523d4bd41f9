/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root';
import { Route as UnauthorizedRouteImport } from './routes/unauthorized';
import { Route as LoginRouteImport } from './routes/login';
import { Route as R404RouteImport } from './routes/404';
import { Route as AuthRouteRouteImport } from './routes/_auth/route';
import { Route as IndexRouteImport } from './routes/index';
import { Route as AuthUsersRouteRouteImport } from './routes/_auth/users/route';
import { Route as AuthResourcesRouteRouteImport } from './routes/_auth/resources/route';
import { Route as AuthPricingRouteRouteImport } from './routes/_auth/pricing/route';
import { Route as AuthLessonsRouteRouteImport } from './routes/_auth/lessons/route';
import { Route as AuthDashboardRouteRouteImport } from './routes/_auth/dashboard/route';
import { Route as AuthCurriculumRouteRouteImport } from './routes/_auth/curriculum/route';
import { Route as AuthUsersStudentsRouteRouteImport } from './routes/_auth/users/students/route';
import { Route as AuthUsersParentsRouteRouteImport } from './routes/_auth/users/parents/route';
import { Route as AuthUsersInstructorsRouteRouteImport } from './routes/_auth/users/instructors/route';
import { Route as AuthUsersCreateStudentRouteRouteImport } from './routes/_auth/users/create-student/route';
import { Route as AuthUsersCreateParentsRouteRouteImport } from './routes/_auth/users/create-parents/route';
import { Route as AuthUsersCreateInstructorRouteRouteImport } from './routes/_auth/users/create-instructor/route';
import { Route as AuthResourcesHorsesAssignmentRouteRouteImport } from './routes/_auth/resources/horses-assignment/route';
import { Route as AuthResourcesHorsesRouteRouteImport } from './routes/_auth/resources/horses/route';
import { Route as AuthResourcesCreateHorseRouteRouteImport } from './routes/_auth/resources/create-horse/route';
import { Route as AuthLessonsHistoryRouteRouteImport } from './routes/_auth/lessons/history/route';
import { Route as AuthLessonsDetailsRouteRouteImport } from './routes/_auth/lessons/details/route';
import { Route as AuthLessonsCreateRouteRouteImport } from './routes/_auth/lessons/create/route';
import { Route as AuthLessonsCalendarRouteRouteImport } from './routes/_auth/lessons/calendar/route';
import { Route as AuthCurriculumBuilderRouteRouteImport } from './routes/_auth/curriculum/builder/route';

const UnauthorizedRoute = UnauthorizedRouteImport.update({
  id: '/unauthorized',
  path: '/unauthorized',
  getParentRoute: () => rootRouteImport,
} as any);
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any);
const R404Route = R404RouteImport.update({
  id: '/404',
  path: '/404',
  getParentRoute: () => rootRouteImport,
} as any);
const AuthRouteRoute = AuthRouteRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any);
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any);
const AuthUsersRouteRoute = AuthUsersRouteRouteImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthResourcesRouteRoute = AuthResourcesRouteRouteImport.update({
  id: '/resources',
  path: '/resources',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthPricingRouteRoute = AuthPricingRouteRouteImport.update({
  id: '/pricing',
  path: '/pricing',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthLessonsRouteRoute = AuthLessonsRouteRouteImport.update({
  id: '/lessons',
  path: '/lessons',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthDashboardRouteRoute = AuthDashboardRouteRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthCurriculumRouteRoute = AuthCurriculumRouteRouteImport.update({
  id: '/curriculum',
  path: '/curriculum',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthUsersStudentsRouteRoute = AuthUsersStudentsRouteRouteImport.update({
  id: '/students',
  path: '/students',
  getParentRoute: () => AuthUsersRouteRoute,
} as any);
const AuthUsersParentsRouteRoute = AuthUsersParentsRouteRouteImport.update({
  id: '/parents',
  path: '/parents',
  getParentRoute: () => AuthUsersRouteRoute,
} as any);
const AuthUsersInstructorsRouteRoute =
  AuthUsersInstructorsRouteRouteImport.update({
    id: '/instructors',
    path: '/instructors',
    getParentRoute: () => AuthUsersRouteRoute,
  } as any);
const AuthUsersCreateStudentRouteRoute =
  AuthUsersCreateStudentRouteRouteImport.update({
    id: '/create-student',
    path: '/create-student',
    getParentRoute: () => AuthUsersRouteRoute,
  } as any);
const AuthUsersCreateParentsRouteRoute =
  AuthUsersCreateParentsRouteRouteImport.update({
    id: '/create-parents',
    path: '/create-parents',
    getParentRoute: () => AuthUsersRouteRoute,
  } as any);
const AuthUsersCreateInstructorRouteRoute =
  AuthUsersCreateInstructorRouteRouteImport.update({
    id: '/create-instructor',
    path: '/create-instructor',
    getParentRoute: () => AuthUsersRouteRoute,
  } as any);
const AuthResourcesHorsesAssignmentRouteRoute =
  AuthResourcesHorsesAssignmentRouteRouteImport.update({
    id: '/horses-assignment',
    path: '/horses-assignment',
    getParentRoute: () => AuthResourcesRouteRoute,
  } as any);
const AuthResourcesHorsesRouteRoute =
  AuthResourcesHorsesRouteRouteImport.update({
    id: '/horses',
    path: '/horses',
    getParentRoute: () => AuthResourcesRouteRoute,
  } as any);
const AuthResourcesCreateHorseRouteRoute =
  AuthResourcesCreateHorseRouteRouteImport.update({
    id: '/create-horse',
    path: '/create-horse',
    getParentRoute: () => AuthResourcesRouteRoute,
  } as any);
const AuthLessonsHistoryRouteRoute = AuthLessonsHistoryRouteRouteImport.update({
  id: '/history',
  path: '/history',
  getParentRoute: () => AuthLessonsRouteRoute,
} as any);
const AuthLessonsDetailsRouteRoute = AuthLessonsDetailsRouteRouteImport.update({
  id: '/details',
  path: '/details',
  getParentRoute: () => AuthLessonsRouteRoute,
} as any);
const AuthLessonsCreateRouteRoute = AuthLessonsCreateRouteRouteImport.update({
  id: '/create',
  path: '/create',
  getParentRoute: () => AuthLessonsRouteRoute,
} as any);
const AuthLessonsCalendarRouteRoute =
  AuthLessonsCalendarRouteRouteImport.update({
    id: '/calendar',
    path: '/calendar',
    getParentRoute: () => AuthLessonsRouteRoute,
  } as any);
const AuthCurriculumBuilderRouteRoute =
  AuthCurriculumBuilderRouteRouteImport.update({
    id: '/builder',
    path: '/builder',
    getParentRoute: () => AuthCurriculumRouteRoute,
  } as any);

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute;
  '/404': typeof R404Route;
  '/login': typeof LoginRoute;
  '/unauthorized': typeof UnauthorizedRoute;
  '/curriculum': typeof AuthCurriculumRouteRouteWithChildren;
  '/dashboard': typeof AuthDashboardRouteRoute;
  '/lessons': typeof AuthLessonsRouteRouteWithChildren;
  '/pricing': typeof AuthPricingRouteRoute;
  '/resources': typeof AuthResourcesRouteRouteWithChildren;
  '/users': typeof AuthUsersRouteRouteWithChildren;
  '/curriculum/builder': typeof AuthCurriculumBuilderRouteRoute;
  '/lessons/calendar': typeof AuthLessonsCalendarRouteRoute;
  '/lessons/create': typeof AuthLessonsCreateRouteRoute;
  '/lessons/details': typeof AuthLessonsDetailsRouteRoute;
  '/lessons/history': typeof AuthLessonsHistoryRouteRoute;
  '/resources/create-horse': typeof AuthResourcesCreateHorseRouteRoute;
  '/resources/horses': typeof AuthResourcesHorsesRouteRoute;
  '/resources/horses-assignment': typeof AuthResourcesHorsesAssignmentRouteRoute;
  '/users/create-instructor': typeof AuthUsersCreateInstructorRouteRoute;
  '/users/create-parents': typeof AuthUsersCreateParentsRouteRoute;
  '/users/create-student': typeof AuthUsersCreateStudentRouteRoute;
  '/users/instructors': typeof AuthUsersInstructorsRouteRoute;
  '/users/parents': typeof AuthUsersParentsRouteRoute;
  '/users/students': typeof AuthUsersStudentsRouteRoute;
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute;
  '/404': typeof R404Route;
  '/login': typeof LoginRoute;
  '/unauthorized': typeof UnauthorizedRoute;
  '/curriculum': typeof AuthCurriculumRouteRouteWithChildren;
  '/dashboard': typeof AuthDashboardRouteRoute;
  '/lessons': typeof AuthLessonsRouteRouteWithChildren;
  '/pricing': typeof AuthPricingRouteRoute;
  '/resources': typeof AuthResourcesRouteRouteWithChildren;
  '/users': typeof AuthUsersRouteRouteWithChildren;
  '/curriculum/builder': typeof AuthCurriculumBuilderRouteRoute;
  '/lessons/calendar': typeof AuthLessonsCalendarRouteRoute;
  '/lessons/create': typeof AuthLessonsCreateRouteRoute;
  '/lessons/details': typeof AuthLessonsDetailsRouteRoute;
  '/lessons/history': typeof AuthLessonsHistoryRouteRoute;
  '/resources/create-horse': typeof AuthResourcesCreateHorseRouteRoute;
  '/resources/horses': typeof AuthResourcesHorsesRouteRoute;
  '/resources/horses-assignment': typeof AuthResourcesHorsesAssignmentRouteRoute;
  '/users/create-instructor': typeof AuthUsersCreateInstructorRouteRoute;
  '/users/create-parents': typeof AuthUsersCreateParentsRouteRoute;
  '/users/create-student': typeof AuthUsersCreateStudentRouteRoute;
  '/users/instructors': typeof AuthUsersInstructorsRouteRoute;
  '/users/parents': typeof AuthUsersParentsRouteRoute;
  '/users/students': typeof AuthUsersStudentsRouteRoute;
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport;
  '/': typeof IndexRoute;
  '/_auth': typeof AuthRouteRouteWithChildren;
  '/404': typeof R404Route;
  '/login': typeof LoginRoute;
  '/unauthorized': typeof UnauthorizedRoute;
  '/_auth/curriculum': typeof AuthCurriculumRouteRouteWithChildren;
  '/_auth/dashboard': typeof AuthDashboardRouteRoute;
  '/_auth/lessons': typeof AuthLessonsRouteRouteWithChildren;
  '/_auth/pricing': typeof AuthPricingRouteRoute;
  '/_auth/resources': typeof AuthResourcesRouteRouteWithChildren;
  '/_auth/users': typeof AuthUsersRouteRouteWithChildren;
  '/_auth/curriculum/builder': typeof AuthCurriculumBuilderRouteRoute;
  '/_auth/lessons/calendar': typeof AuthLessonsCalendarRouteRoute;
  '/_auth/lessons/create': typeof AuthLessonsCreateRouteRoute;
  '/_auth/lessons/details': typeof AuthLessonsDetailsRouteRoute;
  '/_auth/lessons/history': typeof AuthLessonsHistoryRouteRoute;
  '/_auth/resources/create-horse': typeof AuthResourcesCreateHorseRouteRoute;
  '/_auth/resources/horses': typeof AuthResourcesHorsesRouteRoute;
  '/_auth/resources/horses-assignment': typeof AuthResourcesHorsesAssignmentRouteRoute;
  '/_auth/users/create-instructor': typeof AuthUsersCreateInstructorRouteRoute;
  '/_auth/users/create-parents': typeof AuthUsersCreateParentsRouteRoute;
  '/_auth/users/create-student': typeof AuthUsersCreateStudentRouteRoute;
  '/_auth/users/instructors': typeof AuthUsersInstructorsRouteRoute;
  '/_auth/users/parents': typeof AuthUsersParentsRouteRoute;
  '/_auth/users/students': typeof AuthUsersStudentsRouteRoute;
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | '/'
    | '/404'
    | '/login'
    | '/unauthorized'
    | '/curriculum'
    | '/dashboard'
    | '/lessons'
    | '/pricing'
    | '/resources'
    | '/users'
    | '/curriculum/builder'
    | '/lessons/calendar'
    | '/lessons/create'
    | '/lessons/details'
    | '/lessons/history'
    | '/resources/create-horse'
    | '/resources/horses'
    | '/resources/horses-assignment'
    | '/users/create-instructor'
    | '/users/create-parents'
    | '/users/create-student'
    | '/users/instructors'
    | '/users/parents'
    | '/users/students';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | '/'
    | '/404'
    | '/login'
    | '/unauthorized'
    | '/curriculum'
    | '/dashboard'
    | '/lessons'
    | '/pricing'
    | '/resources'
    | '/users'
    | '/curriculum/builder'
    | '/lessons/calendar'
    | '/lessons/create'
    | '/lessons/details'
    | '/lessons/history'
    | '/resources/create-horse'
    | '/resources/horses'
    | '/resources/horses-assignment'
    | '/users/create-instructor'
    | '/users/create-parents'
    | '/users/create-student'
    | '/users/instructors'
    | '/users/parents'
    | '/users/students';
  id:
    | '__root__'
    | '/'
    | '/_auth'
    | '/404'
    | '/login'
    | '/unauthorized'
    | '/_auth/curriculum'
    | '/_auth/dashboard'
    | '/_auth/lessons'
    | '/_auth/pricing'
    | '/_auth/resources'
    | '/_auth/users'
    | '/_auth/curriculum/builder'
    | '/_auth/lessons/calendar'
    | '/_auth/lessons/create'
    | '/_auth/lessons/details'
    | '/_auth/lessons/history'
    | '/_auth/resources/create-horse'
    | '/_auth/resources/horses'
    | '/_auth/resources/horses-assignment'
    | '/_auth/users/create-instructor'
    | '/_auth/users/create-parents'
    | '/_auth/users/create-student'
    | '/_auth/users/instructors'
    | '/_auth/users/parents'
    | '/_auth/users/students';
  fileRoutesById: FileRoutesById;
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  AuthRouteRoute: typeof AuthRouteRouteWithChildren;
  R404Route: typeof R404Route;
  LoginRoute: typeof LoginRoute;
  UnauthorizedRoute: typeof UnauthorizedRoute;
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/unauthorized': {
      id: '/unauthorized';
      path: '/unauthorized';
      fullPath: '/unauthorized';
      preLoaderRoute: typeof UnauthorizedRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/login': {
      id: '/login';
      path: '/login';
      fullPath: '/login';
      preLoaderRoute: typeof LoginRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/404': {
      id: '/404';
      path: '/404';
      fullPath: '/404';
      preLoaderRoute: typeof R404RouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/_auth': {
      id: '/_auth';
      path: '';
      fullPath: '';
      preLoaderRoute: typeof AuthRouteRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/': {
      id: '/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof IndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/_auth/users': {
      id: '/_auth/users';
      path: '/users';
      fullPath: '/users';
      preLoaderRoute: typeof AuthUsersRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/resources': {
      id: '/_auth/resources';
      path: '/resources';
      fullPath: '/resources';
      preLoaderRoute: typeof AuthResourcesRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/pricing': {
      id: '/_auth/pricing';
      path: '/pricing';
      fullPath: '/pricing';
      preLoaderRoute: typeof AuthPricingRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/lessons': {
      id: '/_auth/lessons';
      path: '/lessons';
      fullPath: '/lessons';
      preLoaderRoute: typeof AuthLessonsRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/dashboard': {
      id: '/_auth/dashboard';
      path: '/dashboard';
      fullPath: '/dashboard';
      preLoaderRoute: typeof AuthDashboardRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/curriculum': {
      id: '/_auth/curriculum';
      path: '/curriculum';
      fullPath: '/curriculum';
      preLoaderRoute: typeof AuthCurriculumRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/users/students': {
      id: '/_auth/users/students';
      path: '/students';
      fullPath: '/users/students';
      preLoaderRoute: typeof AuthUsersStudentsRouteRouteImport;
      parentRoute: typeof AuthUsersRouteRoute;
    };
    '/_auth/users/parents': {
      id: '/_auth/users/parents';
      path: '/parents';
      fullPath: '/users/parents';
      preLoaderRoute: typeof AuthUsersParentsRouteRouteImport;
      parentRoute: typeof AuthUsersRouteRoute;
    };
    '/_auth/users/instructors': {
      id: '/_auth/users/instructors';
      path: '/instructors';
      fullPath: '/users/instructors';
      preLoaderRoute: typeof AuthUsersInstructorsRouteRouteImport;
      parentRoute: typeof AuthUsersRouteRoute;
    };
    '/_auth/users/create-student': {
      id: '/_auth/users/create-student';
      path: '/create-student';
      fullPath: '/users/create-student';
      preLoaderRoute: typeof AuthUsersCreateStudentRouteRouteImport;
      parentRoute: typeof AuthUsersRouteRoute;
    };
    '/_auth/users/create-parents': {
      id: '/_auth/users/create-parents';
      path: '/create-parents';
      fullPath: '/users/create-parents';
      preLoaderRoute: typeof AuthUsersCreateParentsRouteRouteImport;
      parentRoute: typeof AuthUsersRouteRoute;
    };
    '/_auth/users/create-instructor': {
      id: '/_auth/users/create-instructor';
      path: '/create-instructor';
      fullPath: '/users/create-instructor';
      preLoaderRoute: typeof AuthUsersCreateInstructorRouteRouteImport;
      parentRoute: typeof AuthUsersRouteRoute;
    };
    '/_auth/resources/horses-assignment': {
      id: '/_auth/resources/horses-assignment';
      path: '/horses-assignment';
      fullPath: '/resources/horses-assignment';
      preLoaderRoute: typeof AuthResourcesHorsesAssignmentRouteRouteImport;
      parentRoute: typeof AuthResourcesRouteRoute;
    };
    '/_auth/resources/horses': {
      id: '/_auth/resources/horses';
      path: '/horses';
      fullPath: '/resources/horses';
      preLoaderRoute: typeof AuthResourcesHorsesRouteRouteImport;
      parentRoute: typeof AuthResourcesRouteRoute;
    };
    '/_auth/resources/create-horse': {
      id: '/_auth/resources/create-horse';
      path: '/create-horse';
      fullPath: '/resources/create-horse';
      preLoaderRoute: typeof AuthResourcesCreateHorseRouteRouteImport;
      parentRoute: typeof AuthResourcesRouteRoute;
    };
    '/_auth/lessons/history': {
      id: '/_auth/lessons/history';
      path: '/history';
      fullPath: '/lessons/history';
      preLoaderRoute: typeof AuthLessonsHistoryRouteRouteImport;
      parentRoute: typeof AuthLessonsRouteRoute;
    };
    '/_auth/lessons/details': {
      id: '/_auth/lessons/details';
      path: '/details';
      fullPath: '/lessons/details';
      preLoaderRoute: typeof AuthLessonsDetailsRouteRouteImport;
      parentRoute: typeof AuthLessonsRouteRoute;
    };
    '/_auth/lessons/create': {
      id: '/_auth/lessons/create';
      path: '/create';
      fullPath: '/lessons/create';
      preLoaderRoute: typeof AuthLessonsCreateRouteRouteImport;
      parentRoute: typeof AuthLessonsRouteRoute;
    };
    '/_auth/lessons/calendar': {
      id: '/_auth/lessons/calendar';
      path: '/calendar';
      fullPath: '/lessons/calendar';
      preLoaderRoute: typeof AuthLessonsCalendarRouteRouteImport;
      parentRoute: typeof AuthLessonsRouteRoute;
    };
    '/_auth/curriculum/builder': {
      id: '/_auth/curriculum/builder';
      path: '/builder';
      fullPath: '/curriculum/builder';
      preLoaderRoute: typeof AuthCurriculumBuilderRouteRouteImport;
      parentRoute: typeof AuthCurriculumRouteRoute;
    };
  }
}

interface AuthCurriculumRouteRouteChildren {
  AuthCurriculumBuilderRouteRoute: typeof AuthCurriculumBuilderRouteRoute;
}

const AuthCurriculumRouteRouteChildren: AuthCurriculumRouteRouteChildren = {
  AuthCurriculumBuilderRouteRoute: AuthCurriculumBuilderRouteRoute,
};

const AuthCurriculumRouteRouteWithChildren =
  AuthCurriculumRouteRoute._addFileChildren(AuthCurriculumRouteRouteChildren);

interface AuthLessonsRouteRouteChildren {
  AuthLessonsCalendarRouteRoute: typeof AuthLessonsCalendarRouteRoute;
  AuthLessonsCreateRouteRoute: typeof AuthLessonsCreateRouteRoute;
  AuthLessonsDetailsRouteRoute: typeof AuthLessonsDetailsRouteRoute;
  AuthLessonsHistoryRouteRoute: typeof AuthLessonsHistoryRouteRoute;
}

const AuthLessonsRouteRouteChildren: AuthLessonsRouteRouteChildren = {
  AuthLessonsCalendarRouteRoute: AuthLessonsCalendarRouteRoute,
  AuthLessonsCreateRouteRoute: AuthLessonsCreateRouteRoute,
  AuthLessonsDetailsRouteRoute: AuthLessonsDetailsRouteRoute,
  AuthLessonsHistoryRouteRoute: AuthLessonsHistoryRouteRoute,
};

const AuthLessonsRouteRouteWithChildren =
  AuthLessonsRouteRoute._addFileChildren(AuthLessonsRouteRouteChildren);

interface AuthResourcesRouteRouteChildren {
  AuthResourcesCreateHorseRouteRoute: typeof AuthResourcesCreateHorseRouteRoute;
  AuthResourcesHorsesRouteRoute: typeof AuthResourcesHorsesRouteRoute;
  AuthResourcesHorsesAssignmentRouteRoute: typeof AuthResourcesHorsesAssignmentRouteRoute;
}

const AuthResourcesRouteRouteChildren: AuthResourcesRouteRouteChildren = {
  AuthResourcesCreateHorseRouteRoute: AuthResourcesCreateHorseRouteRoute,
  AuthResourcesHorsesRouteRoute: AuthResourcesHorsesRouteRoute,
  AuthResourcesHorsesAssignmentRouteRoute:
    AuthResourcesHorsesAssignmentRouteRoute,
};

const AuthResourcesRouteRouteWithChildren =
  AuthResourcesRouteRoute._addFileChildren(AuthResourcesRouteRouteChildren);

interface AuthUsersRouteRouteChildren {
  AuthUsersCreateInstructorRouteRoute: typeof AuthUsersCreateInstructorRouteRoute;
  AuthUsersCreateParentsRouteRoute: typeof AuthUsersCreateParentsRouteRoute;
  AuthUsersCreateStudentRouteRoute: typeof AuthUsersCreateStudentRouteRoute;
  AuthUsersInstructorsRouteRoute: typeof AuthUsersInstructorsRouteRoute;
  AuthUsersParentsRouteRoute: typeof AuthUsersParentsRouteRoute;
  AuthUsersStudentsRouteRoute: typeof AuthUsersStudentsRouteRoute;
}

const AuthUsersRouteRouteChildren: AuthUsersRouteRouteChildren = {
  AuthUsersCreateInstructorRouteRoute: AuthUsersCreateInstructorRouteRoute,
  AuthUsersCreateParentsRouteRoute: AuthUsersCreateParentsRouteRoute,
  AuthUsersCreateStudentRouteRoute: AuthUsersCreateStudentRouteRoute,
  AuthUsersInstructorsRouteRoute: AuthUsersInstructorsRouteRoute,
  AuthUsersParentsRouteRoute: AuthUsersParentsRouteRoute,
  AuthUsersStudentsRouteRoute: AuthUsersStudentsRouteRoute,
};

const AuthUsersRouteRouteWithChildren = AuthUsersRouteRoute._addFileChildren(
  AuthUsersRouteRouteChildren,
);

interface AuthRouteRouteChildren {
  AuthCurriculumRouteRoute: typeof AuthCurriculumRouteRouteWithChildren;
  AuthDashboardRouteRoute: typeof AuthDashboardRouteRoute;
  AuthLessonsRouteRoute: typeof AuthLessonsRouteRouteWithChildren;
  AuthPricingRouteRoute: typeof AuthPricingRouteRoute;
  AuthResourcesRouteRoute: typeof AuthResourcesRouteRouteWithChildren;
  AuthUsersRouteRoute: typeof AuthUsersRouteRouteWithChildren;
}

const AuthRouteRouteChildren: AuthRouteRouteChildren = {
  AuthCurriculumRouteRoute: AuthCurriculumRouteRouteWithChildren,
  AuthDashboardRouteRoute: AuthDashboardRouteRoute,
  AuthLessonsRouteRoute: AuthLessonsRouteRouteWithChildren,
  AuthPricingRouteRoute: AuthPricingRouteRoute,
  AuthResourcesRouteRoute: AuthResourcesRouteRouteWithChildren,
  AuthUsersRouteRoute: AuthUsersRouteRouteWithChildren,
};

const AuthRouteRouteWithChildren = AuthRouteRoute._addFileChildren(
  AuthRouteRouteChildren,
);

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthRouteRoute: AuthRouteRouteWithChildren,
  R404Route: R404Route,
  LoginRoute: LoginRoute,
  UnauthorizedRoute: UnauthorizedRoute,
};
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

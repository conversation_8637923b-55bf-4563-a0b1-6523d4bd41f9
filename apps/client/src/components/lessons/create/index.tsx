import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { type CreateLessonPayload } from 'shared/src/types/lessons';

import { Button } from '@/components/ui/button';
import { useCreateLesson } from '@/hooks/useCreateLesson';

import { AdditionalDetailsCard } from './additional-card';
import { InstructorsCard } from './Instructors-card';
import { LessonDetailsForm } from './lesson-details-form';
import { RequirementsCard } from './requirements-card';

interface LessonFormData {
  eventName: string;
  lessonType: string;
  arena: string;
  duration: string;
  curriculum: Array<string>;
  maxStudents: number;
  date: string;
  time: string;
  isRecurring: boolean;
  recurrenceType: 'weekly' | 'biweekly' | 'monthly';
  selectedDays: Array<string>;
  endDate: string;
  instructors: Array<string>;
  notes: string;
  requireForm: boolean;
  requirePayment: boolean;
}

const lessonTypeOptions = [
  { value: 'single-lesson', label: 'Single Lesson' },
  { value: 'recurring-lesson', label: 'Recurring Lesson' },
  { value: 'camp', label: 'Camp' },
];

const arenaOptions = [
  { value: 'main-arena', label: 'Main Arena' },
  { value: 'practice-ring', label: 'Practice Ring' },
  { value: 'indoor-arena', label: 'Indoor Arena' },
  { value: 'outdoor-arena', label: 'Outdoor Arena' },
  { value: 'round-pen', label: 'Round Pen' },
  { value: 'trail-course', label: 'Trail Course' },
];

const durationOptions = [
  { value: '30', label: '30 minutes' },
  { value: '45', label: '45 minutes' },
  { value: '60', label: '1 hour' },
  { value: '90', label: '1.5 hours' },
  { value: '120', label: '2 hours' },
];

const curriculumOptions = [
  { value: 'basic-horsemanship', label: 'Basic Horsemanship' },
  { value: 'english-riding', label: 'English Riding' },
  { value: 'western-riding', label: 'Western Riding' },
  { value: 'dressage-fundamentals', label: 'Dressage Fundamentals' },
  { value: 'jumping-basics', label: 'Jumping Basics' },
  { value: 'advanced-jumping', label: 'Advanced Jumping' },
  { value: 'trail-safety', label: 'Trail Safety' },
  { value: 'competition-prep', label: 'Competition Preparation' },
];

const instructorOptions = [
  { value: 'sarah-johnson', label: 'Sarah Johnson' },
  { value: 'mike-davis', label: 'Mike Davis' },
  { value: 'anna-wilson', label: 'Anna Wilson' },
  { value: 'david-brown', label: 'David Brown' },
  { value: 'jessica-taylor', label: 'Jessica Taylor' },
  { value: 'usman-ali', label: 'Usman Ali' },
  { value: 'emma-clark', label: 'Emma Clark' },
  { value: 'james-miller', label: 'James Miller' },
];

export function CreateLesson() {
  const [instructorSearch, setInstructorSearch] = useState('');
  const { mutateAsync: createLesson, isPending } = useCreateLesson();

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    // formState: { errors },
  } = useForm<LessonFormData>({
    defaultValues: {
      eventName: '',
      lessonType: '',
      arena: '',
      duration: '',
      curriculum: [],
      maxStudents: 4,
      date: '',
      time: '',
      isRecurring: false,
      recurrenceType: 'weekly',
      selectedDays: [],
      endDate: '',
      instructors: [],
      notes: '',
      requireForm: false,
      requirePayment: false,
    },
  });

  const watchedInstructors = watch('instructors');
  const watchedSelectedDays = watch('selectedDays');
  const watchedCurriculum = watch('curriculum');

  const onSubmit = async (data: LessonFormData) => {
    try {
      // Transform form data to API payload format
      const payload: CreateLessonPayload = {
        title: data.eventName,
        lesson_type: data.lessonType as 'single-lesson' | 'recurring-lesson' | 'camp',
        arena_id: data.arena ? parseInt(data.arena) : undefined,
        date: data.date,
        start_time: `${data.date}T${data.time}:00.000Z`,
        end_time: calculateEndTime(data.date, data.time, data.duration),
        duration_minutes: parseInt(data.duration),
        max_students: data.maxStudents,
        notes: data.notes || undefined,
        require_form: data.requireForm,
        require_payment: data.requirePayment,
        curriculum_items: data.curriculum,
        instructor_ids: data.instructors,
        recurrence_pattern: data.isRecurring ? {
          type: data.recurrenceType,
          interval: 1,
          daysOfWeek: data.selectedDays.map(day => parseInt(day)),
          endDate: data.endDate || undefined,
        } : undefined,
      };

      await createLesson(payload);
      console.log('Lesson created successfully!');

      // Reset form or redirect as needed
      // You might want to add a success toast here
    } catch (error) {
      console.error('Failed to create lesson:', error);
      // You might want to add an error toast here
    }
  };

  // Helper function to calculate end time
  const calculateEndTime = (date: string, time: string, duration: string): string => {
    const startDateTime = new Date(`${date}T${time}:00.000Z`);
    const durationMinutes = parseInt(duration);
    const endDateTime = new Date(startDateTime.getTime() + durationMinutes * 60000);
    return endDateTime.toISOString();
  };

  const addInstructor = (instructorId: string) => {
    const currentInstructors = watchedInstructors;
    if (!currentInstructors.includes(instructorId)) {
      setValue('instructors', [...currentInstructors, instructorId]);
    }
    setInstructorSearch('');
  };

  const removeInstructor = (instructorId: string) => {
    const currentInstructors = watchedInstructors;
    setValue(
      'instructors',
      currentInstructors.filter((id) => id !== instructorId),
    );
  };

  const addCurriculum = (curriculumId: string) => {
    const currentCurriculum = watchedCurriculum;
    if (!currentCurriculum.includes(curriculumId)) {
      setValue('curriculum', [...currentCurriculum, curriculumId]);
    }
  };

  const removeCurriculum = (curriculumId: string) => {
    const currentCurriculum = watchedCurriculum;
    setValue(
      'curriculum',
      currentCurriculum.filter((id) => id !== curriculumId),
    );
  };

  const filteredInstructors = instructorOptions.filter(
    (instructor) =>
      instructor.label.toLowerCase().includes(instructorSearch.toLowerCase()) &&
      !watchedInstructors.includes(instructor.value),
  );

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='mx-auto max-w-6xl'>
        {/* Header */}
        <div className='mb-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm'>
          <h1 className='mb-2 text-3xl font-bold text-gray-900'>
            Create Lesson
          </h1>
          <p className='text-gray-600'>
            Set up a new lesson for your riding school
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className='grid grid-cols-1 gap-8 lg:grid-cols-2'>
            {/* Left Column - Lesson Details */}
            <div className='space-y-6'>
              {/* Lesson Details Card */}
              <LessonDetailsForm
                control={control}
                watch={watch}
                watchedCurriculum={watchedCurriculum}
                watchedSelectedDays={watchedSelectedDays}
                setValue={setValue}
                lessonTypeOptions={lessonTypeOptions}
                arenaOptions={arenaOptions}
                durationOptions={durationOptions}
                curriculumOptions={curriculumOptions}
                removeCurriculum={removeCurriculum}
                addCurriculum={addCurriculum}
              />
            </div>

            {/* Right Column - Staff & Resources */}
            <div className='space-y-6'>
              {/* Instructors Card */}
              <InstructorsCard
                watchedInstructors={watchedInstructors}
                instructorOptions={instructorOptions}
                instructorSearch={instructorSearch}
                setInstructorSearch={setInstructorSearch}
                filteredInstructors={filteredInstructors}
                addInstructor={addInstructor}
                removeInstructor={removeInstructor}
              />

              {/* Requirements Card */}
              <RequirementsCard control={control} />
              {/* Additional Details Card */}
              <AdditionalDetailsCard control={control} />
            </div>
          </div>

          {/* Floating Create Lesson Button */}
          <div className='fixed right-8 bottom-8'>
            <Button
              type='submit'
              size='lg'
              disabled={isPending}
              className='rounded-xl bg-[#5C67F2] px-8 py-4 text-white shadow-lg transition-all duration-200 hover:bg-[#5C67F2]/90 hover:shadow-xl disabled:opacity-50'
            >
              <Plus className='mr-2 h-5 w-5' />
              {isPending ? 'Creating...' : 'Create Lesson'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

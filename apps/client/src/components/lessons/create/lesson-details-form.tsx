// LessonDetailsCard.tsx
import { Calendar, Clock, X } from 'lucide-react';
import {
  type Control,
  Controller,
  type UseFormSetValue,
  type FieldErrors,
} from 'react-hook-form';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

import { cn } from '@/lib/utils';

// LessonDetails.types.ts

export interface LessonFormData {
  eventName: string;
  lessonType: string;
  arena: string;
  duration: string;
  curriculum: Array<string>;
  maxStudents: number;
  date: string;
  time: string;
  isRecurring: boolean;
  recurrenceType: 'weekly' | 'biweekly' | 'monthly';
  selectedDays: Array<string>;
  endDate: string;
  instructors: Array<string>;
  notes: string;
  requireForm: boolean;
  requirePayment: boolean;
}

interface LessonDetailsFormProps {
  control: Control<LessonFormData>;
  watch: <T extends keyof LessonFormData>(field: T) => LessonFormData[T];
  watchedCurriculum: Array<string>;
  watchedSelectedDays: Array<string>;
  lessonTypeOptions: Array<{ value: string; label: string }>;
  arenaOptions: Array<{ value: string; label: string }>;
  durationOptions: Array<{ value: string; label: string }>;
  curriculumOptions: Array<{ value: string; label: string }>;
  removeCurriculum: (curriculumId: string) => void;
  addCurriculum: (curriculumId: string) => void;
  setValue: UseFormSetValue<LessonFormData>;
  errors: FieldErrors<LessonFormData>;
}

export const LessonDetailsForm = ({
  control,
  watch,
  watchedCurriculum,
  watchedSelectedDays,
  lessonTypeOptions,
  arenaOptions,
  durationOptions,
  curriculumOptions,
  removeCurriculum,
  setValue,
  addCurriculum,
  errors,
}: LessonDetailsFormProps) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='text-lg font-semibold text-gray-900'>
          Lesson Details
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6'>
        {/* Event Name */}
        <div>
          <Label
            htmlFor='eventName'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Event Name
          </Label>
          <Controller
            name='eventName'
            control={control}
            rules={{ required: 'Event name is required' }}
            render={({ field }) => (
              <div>
                <Input
                  {...field}
                  placeholder='Enter event name'
                  className='h-11 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'
                />
                {errors.eventName && (
                  <p className='mt-1 text-sm text-red-600'>{errors.eventName.message}</p>
                )}
              </div>
            )}
          />
        </div>

        {/* Lesson Type */}
        <div>
          <Label
            htmlFor='lessonType'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Lesson Type
          </Label>
          <Controller
            name='lessonType'
            control={control}
            rules={{ required: 'Lesson type is required' }}
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className='h-11 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'>
                  <SelectValue placeholder='Select lesson type' />
                </SelectTrigger>
                <SelectContent>
                  {lessonTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </div>

        {/* Arena */}
        <div>
          <Label
            htmlFor='arena'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Arena
          </Label>
          <Controller
            name='arena'
            control={control}
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className='h-11 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'>
                  <SelectValue placeholder='Select arena' />
                </SelectTrigger>
                <SelectContent>
                  {arenaOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </div>

        {/* Duration and Curriculum */}
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <Label
              htmlFor='duration'
              className='mb-2 block text-sm font-medium text-gray-700'
            >
              Duration
            </Label>
            <Controller
              name='duration'
              control={control}
              rules={{ required: 'Duration is required' }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className='h-11 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'>
                    <SelectValue placeholder='Duration' />
                  </SelectTrigger>
                  <SelectContent>
                    {durationOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div>
            <Label
              htmlFor='curriculum'
              className='mb-2 block text-sm font-medium text-gray-700'
            >
              Curriculum
            </Label>
            <div className='mb-3 flex flex-wrap gap-2'>
              {watchedCurriculum.map((curriculumId) => {
                const curriculum = curriculumOptions.find(
                  (c) => c.value === curriculumId,
                );
                return (
                  <Badge
                    key={curriculumId}
                    variant='secondary'
                    className='rounded-full border border-orange-200 bg-orange-100 px-3 py-1 text-orange-800'
                  >
                    {curriculum?.label}
                    <X
                      className='ml-2 h-3 w-3 cursor-pointer'
                      onClick={() => {
                        removeCurriculum(curriculumId);
                      }}
                    />
                  </Badge>
                );
              })}
            </div>
            <Select onValueChange={addCurriculum}>
              <SelectTrigger className='h-11 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'>
                <SelectValue placeholder='Add curriculum' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='none'>None</SelectItem>
                {curriculumOptions
                  .filter((option) => !watchedCurriculum.includes(option.value))
                  .map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Max Students */}
        <div>
          <Label
            htmlFor='maxStudents'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Max Students
          </Label>
          <Controller
            name='maxStudents'
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                type='number'
                min='1'
                max='20'
                onChange={(e) => {
                  field.onChange(Number.parseInt(e.target.value) || 0);
                }}
                className='h-11 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'
                placeholder='Enter max students'
              />
            )}
          />
        </div>

        {/* Date and Time */}
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <Label
              htmlFor='date'
              className='mb-2 block text-sm font-medium text-gray-700'
            >
              Date
            </Label>
            <Controller
              name='date'
              control={control}
              rules={{ required: 'Date is required' }}
              render={({ field }) => (
                <div className='relative'>
                  <Input
                    {...field}
                    type='date'
                    className='h-11 border-gray-300 bg-white pl-10 focus:border-[#5C67F2] focus:ring-[#5C67F2]'
                  />
                  <Calendar className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400' />
                </div>
              )}
            />
          </div>
          <div>
            <Label
              htmlFor='time'
              className='mb-2 block text-sm font-medium text-gray-700'
            >
              Time
            </Label>
            <Controller
              name='time'
              control={control}
              rules={{ required: 'Time is required' }}
              render={({ field }) => (
                <div className='relative'>
                  <Input
                    {...field}
                    type='time'
                    className='h-11 border-gray-300 bg-white pl-10 focus:border-[#5C67F2] focus:ring-[#5C67F2]'
                  />
                  <Clock className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400' />
                </div>
              )}
            />
          </div>
        </div>

        {/* Recurring Lesson */}
        <div className='space-y-4'>
          <div className='flex items-center space-x-3'>
            <Controller
              name='isRecurring'
              control={control}
              render={({ field }) => (
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  className='data-[state=checked]:bg-[#5C67F2]'
                />
              )}
            />
            <Label className='text-sm font-medium text-gray-700'>
              Make this a recurring lesson
            </Label>
          </div>

          {Boolean(watch('isRecurring')) && (
            <div className='rounded-lg border border-gray-200 bg-white p-6 shadow-sm'>
              <h3 className='mb-6 text-lg font-semibold text-gray-900'>
                Custom recurrence
              </h3>

              {/* Repeat every */}
              <div className='mb-6'>
                <Label className='mb-3 block text-sm font-medium text-gray-600'>
                  Repeat every
                </Label>
                <div className='flex items-center space-x-3'>
                  <Input
                    type='number'
                    min='1'
                    defaultValue='1'
                    className='h-10 w-20 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'
                  />
                  <Controller
                    name='recurrenceType'
                    control={control}
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger className='h-10 w-32 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'>
                          <SelectValue placeholder='week' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='weekly'>week</SelectItem>
                          <SelectItem value='biweekly'>2 weeks</SelectItem>
                          <SelectItem value='monthly'>month</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>

              {/* Repeat on */}
              <div className='mb-6'>
                <Label className='mb-3 block text-sm font-medium text-gray-600'>
                  Repeat on
                </Label>
                <div className='flex space-x-2'>
                  {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => {
                    const dayValue = [
                      'sunday',
                      'monday',
                      'tuesday',
                      'wednesday',
                      'thursday',
                      'friday',
                      'saturday',
                    ][index];
                    return (
                      <Button
                        key={`${day}-${index}`}
                        type='button'
                        variant={
                          watchedSelectedDays.includes(dayValue)
                            ? 'default'
                            : 'outline'
                        }
                        size='sm'
                        onClick={() => {
                          const currentDays = watchedSelectedDays;
                          const updatedDays = currentDays.includes(dayValue)
                            ? currentDays.filter((d) => d !== dayValue)
                            : [...currentDays, dayValue];
                          setValue('selectedDays', updatedDays);
                        }}
                        className={cn(
                          'h-10 w-10 rounded-full text-sm font-medium',
                          watchedSelectedDays.includes(dayValue)
                            ? 'border-[#5C67F2] bg-[#5C67F2] text-white hover:bg-[#5C67F2]/90'
                            : 'border-gray-300 bg-white text-gray-600 hover:bg-gray-50',
                        )}
                      >
                        {day}
                      </Button>
                    );
                  })}
                </div>
              </div>

              {/* Ends */}
              <div className='mb-6'>
                <Label className='mb-3 block text-sm font-medium text-gray-600'>
                  Ends
                </Label>
                <div className='space-y-3'>
                  <div className='flex items-center space-x-3'>
                    <input
                      type='radio'
                      id='never'
                      name='endType'
                      className='h-4 w-4 border-gray-300 text-[#5C67F2] focus:ring-[#5C67F2]'
                      defaultChecked
                    />
                    <Label
                      htmlFor='never'
                      className='cursor-pointer text-sm text-gray-700'
                    >
                      Never
                    </Label>
                  </div>

                  <div className='flex items-center space-x-3'>
                    <input
                      type='radio'
                      id='on-date'
                      name='endType'
                      className='h-4 w-4 border-gray-300 text-[#5C67F2] focus:ring-[#5C67F2]'
                    />
                    <Label
                      htmlFor='on-date'
                      className='cursor-pointer text-sm text-gray-700'
                    >
                      On
                    </Label>
                    <Controller
                      name='endDate'
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type='date'
                          className='h-10 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'
                        />
                      )}
                    />
                  </div>

                  <div className='flex items-center space-x-3'>
                    <input
                      type='radio'
                      id='after-occurrences'
                      name='endType'
                      className='h-4 w-4 border-gray-300 text-[#5C67F2] focus:ring-[#5C67F2]'
                    />
                    <Label
                      htmlFor='after-occurrences'
                      className='cursor-pointer text-sm text-gray-700'
                    >
                      After
                    </Label>
                    <Input
                      type='number'
                      min='1'
                      defaultValue='13'
                      className='h-10 w-20 border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'
                    />
                    <span className='text-sm text-gray-600'>occurrences</span>
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className='flex justify-end space-x-3 border-t border-gray-200 pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => {
                    setValue('isRecurring', false);
                  }}
                  className='border-gray-300 bg-white px-6 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50'
                >
                  Cancel
                </Button>
                <Button
                  type='button'
                  className='bg-[#5C67F2] px-6 py-2 text-sm font-medium text-white hover:bg-[#5C67F2]/90'
                >
                  Done
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

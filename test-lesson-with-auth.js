// Test lesson creation with proper auth token
// First, let's get a valid auth token by signing in

async function testLessonCreation() {
  try {
    // Step 1: Sign in to get a valid token
    console.log('🔐 Attempting to sign in...');
    const signInResponse = await fetch('http://localhost:3000/api/v1/auth/sign-in', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>', // Use a test email
        password: 'password123'     // Use a test password
      }),
    });

    const signInData = await signInResponse.json();
    console.log('🔐 Sign in response:', JSON.stringify(signInData, null, 2));

    if (!signInResponse.ok) {
      console.log('❌ Sign in failed, using dummy token for validation test');
      // Continue with dummy token to test validation
    }

    const accessToken = signInData.data?.accessToken || 'dummy-token';

    // Step 2: Create lesson with the token
    console.log('📚 Creating lesson...');
    const lessonPayload = {
      title: 'Test Lesson from Script',
      lesson_type: 'single-lesson',
      date: new Date().toISOString(),
      start_time: new Date().toISOString(),
      end_time: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour later
      duration_minutes: 60,
      max_students: 1,
      instructor_ids: ['550e8400-e29b-41d4-a716-446655440000']
    };

    console.log('📚 Lesson payload:', JSON.stringify(lessonPayload, null, 2));

    const lessonResponse = await fetch('http://localhost:3000/api/v1/lessons', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify(lessonPayload),
    });

    const lessonData = await lessonResponse.json();
    console.log('📚 Lesson creation response:', JSON.stringify(lessonData, null, 2));
    console.log('📚 Response status:', lessonResponse.status);

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testLessonCreation();
